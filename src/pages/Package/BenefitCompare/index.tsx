import handpickIcon from '@/assets/nike.svg';
import { ConnectState } from '@/models/connect';
import { CloudStorageBenefit, ProductSku } from '@/models/product/interface';
import type { Dispatch } from '@umijs/max';
import { useDispatch, useSelector } from '@umijs/max';
import { Image } from 'antd-mobile';
import { DownOutline } from 'antd-mobile-icons';
import React, { useEffect, useMemo, useState } from 'react';
import { BenefitCompareData, FeatureValue } from './interface';
import {
  processYearlySkuList,
  transformCloudStorageToBenefitCompare,
  transformSkuListToBenefitCompare,
} from './utils';

interface BenefitCompareTableProps {
  data?: BenefitCompareData;
  yearlyProductSkuList: ProductSku[];
}

const BenefitCompareTable: React.FC<BenefitCompareTableProps> = ({
  data,
  yearlyProductSkuList = [],
}) => {
  const dispatch: Dispatch = useDispatch();

  const [benefitData, setBenefitData] = useState<BenefitCompareData>({
    features: [],
    plans: [],
  });
  // 使用传入的 data 或内部状态的 benefitData
  const displayData = data || benefitData;
  // 动态计算grid列模板 - 自适应屏幕宽度
  const planCount = displayData.plans.length;

  const [loading, setLoading] = useState<boolean>(false);

  // 计算可视区域宽度和列数
  const [containerWidth, setContainerWidth] = useState<number>(0);
  const containerRef = React.useRef<HTMLDivElement>(null);

  // 计算自适应列宽 - 使用useMemo优化性能
  const gridConfig = useMemo(() => {
    if (!containerWidth)
      return {
        gridTemplateColumns: '',
        minTableWidth: 0,
        needsHorizontalScroll: false,
        actualPlanColumnWidth: 80,
        featureColumnWidth: 120,
      };

    // 根据屏幕宽度调整列宽参数
    const isMobile = containerWidth < 768;
    const featureColumnWidth = isMobile ? 100 : 120; // 移动端功能名称列稍窄
    const minPlanColumnWidth = isMobile ? 70 : 80; // 移动端套餐列最小宽度
    const maxPlanColumnWidth = isMobile ? 100 : 120; // 移动端套餐列最大宽度
    const availableWidth = containerWidth - featureColumnWidth;

    // 确保最少显示2列套餐，最多根据屏幕宽度自适应
    const minVisiblePlans = Math.min(2, planCount);
    const maxVisiblePlans = Math.max(
      minVisiblePlans,
      Math.floor(availableWidth / minPlanColumnWidth),
    );

    // 如果套餐数量超过可视列数，启用水平滚动
    const needsHorizontalScroll = planCount > maxVisiblePlans;

    // 计算实际列宽
    let actualPlanColumnWidth;
    if (needsHorizontalScroll) {
      actualPlanColumnWidth = minPlanColumnWidth;
    } else {
      // 在不需要滚动时，让列宽自适应但不超过最大宽度
      const calculatedWidth = availableWidth / planCount;
      actualPlanColumnWidth = Math.min(
        maxPlanColumnWidth,
        Math.max(minPlanColumnWidth, calculatedWidth),
      );
    }

    const gridTemplateColumns = `${featureColumnWidth}px repeat(${planCount}, ${actualPlanColumnWidth}px)`;

    // 计算表格最小宽度
    const minTableWidth =
      featureColumnWidth + planCount * actualPlanColumnWidth;

    return {
      gridTemplateColumns,
      minTableWidth,
      needsHorizontalScroll,
      actualPlanColumnWidth,
      featureColumnWidth,
    };
  }, [containerWidth, planCount]);

  // 从dva store中获取defaultBenefitList
  const defaultBenefitList: CloudStorageBenefit[] = useSelector(
    ({ product }: ConnectState) => product.defaultBenefitList,
  );

  // 使用 useMemo 来稳定化 yearlyProductSkuList 的依赖
  const skuListLength = yearlyProductSkuList.length;
  const skuListIds = useMemo(
    () => yearlyProductSkuList.map((sku) => sku.id).join(','),
    [yearlyProductSkuList],
  );

  useEffect(() => {
    const fetchBenefitData = () => {
      try {
        setLoading(true);

        // 优先使用传入的SKU数据
        if (yearlyProductSkuList && yearlyProductSkuList.length > 0) {
          const processedSkuList = processYearlySkuList(yearlyProductSkuList);
          const skuBasedData =
            transformSkuListToBenefitCompare(processedSkuList);
          setBenefitData(skuBasedData);
          setLoading(false);
          return;
        }

        // 如果没有SKU数据，则通过dispatch获取默认权益数据
        // dispatch({
        //   type: 'product/requestDefaultBenefitList',
        // });
      } catch (err) {
        console.error('获取权益数据失败:', err);
        setBenefitData({ features: [], plans: [] });
        setLoading(false);
      }
    };

    fetchBenefitData();
  }, [skuListLength, skuListIds, dispatch]);

  // 监听defaultBenefitList的变化，当数据获取成功后进行转换
  useEffect(() => {
    if (defaultBenefitList && defaultBenefitList.length > 0) {
      try {
        const transformedData =
          transformCloudStorageToBenefitCompare(defaultBenefitList);
        setBenefitData(transformedData);
        console.log(transformedData);
      } catch (err) {
        console.error('转换权益数据失败:', err);
        setBenefitData({ features: [], plans: [] });
      } finally {
        setLoading(false);
      }
    } else if (
      // 如果没有SKU数据且defaultBenefitList为空，设置为空数据
      (!yearlyProductSkuList || yearlyProductSkuList.length === 0) &&
      defaultBenefitList &&
      defaultBenefitList.length === 0
    ) {
      setBenefitData({ features: [], plans: [] });
      setLoading(false);
    }
  }, [defaultBenefitList, yearlyProductSkuList]);

  // 监听容器宽度变化
  useEffect(() => {
    const updateWidth = () => {
      if (containerRef.current) {
        setContainerWidth(containerRef.current.offsetWidth);
      }
    };

    updateWidth();
    window.addEventListener('resize', updateWidth);
    return () => window.removeEventListener('resize', updateWidth);
  }, []);

  const gridCelStyle = {
    gridTemplateColumns: gridConfig.gridTemplateColumns,
    minWidth: `${gridConfig.minTableWidth}px`,
    width: gridConfig.needsHorizontalScroll
      ? `${gridConfig.minTableWidth}px`
      : '100%',
  };

  // 渲染功能值的通用方法
  const renderFeatureValue = (value: FeatureValue): React.ReactNode => {
    if (typeof value === 'boolean') {
      return value ? (
        <Image
          src={handpickIcon}
          className="w-3.5 h-3.5 mx-auto md:w-3 md:h-3"
          alt="支持"
        />
      ) : null;
    }
    return (
      <span className="text-0.5xl font-bold active-color md:text-[11px]">
        {value}
      </span>
    );
  };

  if (loading) {
    return (
      <div className="">
        <h2 className="text-center font-bold text-base mb-4 active-color">
          Compare PETKIT Care+ Plans
        </h2>
        <div className="flex justify-center items-center py-8">
          <div className="text-gray-500">数据加载中...</div>
        </div>
      </div>
    );
  }

  // 如果没有任何benefit数据，返回空组件
  if (!displayData.features || displayData.features.length === 0) {
    return <></>;
  }

  return (
    <div className="">
      <h2 className="text-center font-bold text-3xl mb-4 active-color">
        权益对比
      </h2>

      <div className="overflow-hidden" ref={containerRef}>
        {/* 滚动提示 */}
        {/* {gridConfig.needsHorizontalScroll && (
          <div className="text-center text-xs secondary-text-color mb-2 opacity-70">
            ← 左右滑动查看更多套餐 →
          </div>
        )} */}
        <div
          className={`overflow-x-auto ${
            gridConfig.needsHorizontalScroll ? 'adaptive-table-scroll' : ''
          }`}
        >
          {/* 表头 - 套餐信息 */}
          <div className="grid gap-0 table-bg-color" style={gridCelStyle}>
            {/* 空白列，对应功能名称列 */}
            <div className="p-1.5 text-center bg-transparent font-semibold md:p-3 border-r border-r-[#f0f0f0]">
              {/* 空白 */}
            </div>

            {/* 套餐列 */}
            {displayData.plans.map((plan, index) => (
              <div
                key={plan.id}
                className={`p-1.5 text-center md:p-3 border-r border-r-[#f0f0f0] last-of-type:border-none overflow-hidden`}
              >
                <div className="active-color font-bold text-xs mb-1 truncate">
                  {plan.name}
                </div>
                <div className="active-color font-bold text-xs mb-0.5 truncate">
                  {plan.currency}
                  {plan.price}
                </div>
                {index !== 0 ? (
                  <div className="secondary-text-color text-[7px] opacity-80">
                    {plan.billing}
                  </div>
                ) : null}
              </div>
            ))}
          </div>

          {/* 表体 - 功能对比 */}
          <div className="w-full" style={gridCelStyle}>
            {displayData.features.map((feature, featureIndex) => (
              <div
                key={feature.id}
                className={`${
                  featureIndex % 2 === 0 ? 'bg-white' : 'table-bg-color'
                }`}
              >
                <div className="grid gap-0" style={gridCelStyle}>
                  {/* 功能名称列 */}
                  <div className="p-1.5 text-left secondary-text-color text-xs font-medium flex items-start md:p-3 md:text-[11px]  border-r border-r-[#f0f0f0] last-of-type:border-none ">
                    <DownOutline
                      className="primary-text-color mr-2 font-bold mt-0.5 flex-shrink-0"
                      fontSize={12}
                    />
                    <div className="flex-1">
                      <div className="primary-text-color text-xs">
                        {feature.name}
                      </div>
                      {feature.description && (
                        <div className="secondary-text-color text-[10px] mt-1 opacity-70 leading-tight md:text-[9px]">
                          {feature.description}
                        </div>
                      )}
                    </div>
                  </div>

                  {/* 各套餐对应的功能值 */}
                  {displayData.plans.map((plan) => (
                    <div
                      key={`${feature.id}-${plan.id}`}
                      className="p-1.5 text-center flex items-center justify-center md:p-3  border-r border-r-[#f0f0f0] last-of-type:border-none "
                    >
                      {renderFeatureValue(plan.features[feature.id])}
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default BenefitCompareTable;
